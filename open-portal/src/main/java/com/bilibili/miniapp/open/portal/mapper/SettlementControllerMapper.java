package com.bilibili.miniapp.open.portal.mapper;

import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDateListVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDateVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementDetailVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementItemVo;
import com.bilibili.miniapp.open.portal.vo.settlement.SettlementPreviewVo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDateBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDateListBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementDetailBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementItemBo;
import com.bilibili.miniapp.open.service.bo.settlement.SettlementPreviewBo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 结算控制器映射器
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Mapper
public interface SettlementControllerMapper extends BaseAmountMapper {

    SettlementControllerMapper MAPPER = Mappers.getMapper(SettlementControllerMapper.class);

    /**
     * 结算日期BO转VO
     */
    SettlementDateVo boToVo(SettlementDateBo bo);

    /**
     * 结算日期列表BO转VO
     */
    SettlementDateListVo boToVo(SettlementDateListBo bo);


    /**
     * 结算预览响应BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawApplyAmount", expression = "java(convertCentToYuan(bo.getWithdrawApplyAmount()))")
    @Mapping(target = "taxFee", expression = "java(convertCentToYuan(bo.getTaxFee()))")
    @Mapping(target = "actualWithdrawAmount", expression = "java(convertCentToYuan(bo.getActualWithdrawAmount()))")
    SettlementPreviewVo boToVo(SettlementPreviewBo bo);

    /**
     * 结算项BO转VO
     * 将分转换为元，保留2位小数
     */
    @Mapping(target = "withdrawApplyAmount", expression = "java(convertCentToYuan(bo.getWithdrawApplyAmount()))")
    @Mapping(target = "taxFee", expression = "java(convertCentToYuan(bo.getTaxFee()))")
    @Mapping(target = "actualWithdrawAmount", expression = "java(convertCentToYuan(bo.getActualWithdrawAmount()))")
    SettlementItemVo boToVo(SettlementItemBo bo);

    /**
     * 结算详情BO转VO
     * 将分转换为元，保留2位小数，时间格式化为字符串
     */
    @Mapping(target = "actualWithdrawAmount", expression = "java(convertCentToYuan(bo.getActualWithdrawAmount()))")
    @Mapping(target = "beginTime", expression = "java(formatTimestamp(bo.getBeginTime()))")
    @Mapping(target = "endTime", expression = "java(formatTimestamp(bo.getEndTime()))")
    SettlementDetailVo detailBoToVo(SettlementDetailBo bo);
}
