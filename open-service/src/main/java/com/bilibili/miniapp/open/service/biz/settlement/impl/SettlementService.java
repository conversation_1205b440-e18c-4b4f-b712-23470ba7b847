package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.common.entity.BFSKey;
import com.bilibili.miniapp.open.common.entity.BFSUploadResult;
import com.bilibili.miniapp.open.common.entity.Page;
import com.bilibili.miniapp.open.common.entity.PageResult;
import com.bilibili.miniapp.open.common.enums.ErrorCodeType;
import com.bilibili.miniapp.open.common.enums.MiniAppPermission;
import com.bilibili.miniapp.open.common.enums.TaxType;
import com.bilibili.miniapp.open.common.enums.WithdrawStatus;
import com.bilibili.miniapp.open.common.exception.ServiceException;
import com.bilibili.miniapp.open.common.util.AssertUtil;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenAccrualDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.dao.MiniAppOpenSettlementDao;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenAccrualPoExample;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPo;
import com.bilibili.miniapp.open.repository.mysql.mallapp.po.MiniAppOpenSettlementPoExample;
import com.bilibili.miniapp.open.service.biz.account.IAccountService;
import com.bilibili.miniapp.open.service.biz.company.ICompanyService;
import com.bilibili.miniapp.open.service.biz.finance.IFinanceService;
import com.bilibili.miniapp.open.service.biz.resource.IBFSService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.ISettlementService;
import com.bilibili.miniapp.open.service.bo.company.CompanyDetailBo;
import com.bilibili.miniapp.open.service.bo.finance.FinanceDetailRespBo;
import com.bilibili.miniapp.open.service.bo.settlement.*;
import com.bilibili.miniapp.open.service.rpc.http.impl.MiniAppRemoteService;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiUploadResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.util.UriComponents;
import org.springframework.web.util.UriComponentsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 结算服务实现
 * 金额单位为分保留2位小数
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Slf4j
@Service
public class SettlementService implements ISettlementService {

    @Autowired
    private IAccountService accountService;

    @Autowired
    private MiniAppOpenAccrualDao accrualDao;

    @Autowired
    private MiniAppOpenSettlementDao settlementDao;

    @Autowired
    private IFinanceService financeService;

    @Autowired
    private IBFSService bfsService;

    @Autowired
    private HuilianyiPaymentService huilianyiPaymentService;

    @Autowired
    private ICompanyService companyService;

    /**
     * 固定比例：(1+6%)，用作计算金额
     */
    private final BigDecimal fixedProportionRatio = new BigDecimal("0.06").add(BigDecimal.ONE);

    @Override
    public SettlementDateListBo getSettlementDates(Long mid, String appId) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andWithdrawStatusEqualTo(WithdrawStatus.WITHDRAWABLE.getCode())
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualPo> accrualList = accrualDao.selectByExample(example);

        List<SettlementDateBo> settlementList = accrualList.stream()
                .map(po -> SettlementDateBo.builder()
                        .date(po.getIncomeDate())
                        .accrualId(po.getAccrualId())
                        .build())
                .collect(Collectors.toList());

        return SettlementDateListBo.builder()
                .settlementList(settlementList)
                .build();
    }

    @Override
    public SettlementPreviewBo getSettlementPreview(Long mid, String appId, List<String> accrualIds) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        if (CollectionUtils.isEmpty(accrualIds)) {
            return SettlementPreviewBo.emptyInstance();
        }

        MiniAppOpenAccrualPoExample example = new MiniAppOpenAccrualPoExample();
        example.createCriteria()
                .andAppIdEqualTo(appId)
                .andAccrualIdIn(accrualIds)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenAccrualPo> accrualList = accrualDao.selectByExample(example);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(accrualList), ErrorCodeType.NO_DATA.getCode(), "未找到对应的预提单数据");

        BigDecimal withdrawApplyAmount = accrualList.stream()
                .map(MiniAppOpenAccrualPo::getTotalAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        FinanceDetailRespBo financeDetail = financeService.getFinanceDetail(mid);
        AssertUtil.isTrue(financeDetail != null && financeDetail.getInvoiceInfo() != null, ErrorCodeType.NO_DATA.getCode(), "未找到财务信息，请先完善财务信息");

        TaxType taxType = TaxType.getByCode(financeDetail.getInvoiceInfo().getTaxType());

        BigDecimal taxFee = calculateTaxFee(withdrawApplyAmount, taxType.getTaxRatio());

        BigDecimal actualWithdrawAmount = calculateActualWithdrawAmount(withdrawApplyAmount, taxType.getTaxRatio());

        return SettlementPreviewBo.builder()
                .withdrawApplyAmount(withdrawApplyAmount)
                .taxFee(taxFee)
                .actualWithdrawAmount(actualWithdrawAmount)
                .build();
    }

    /**
     * 由于可支持的文件类型比较多，需要解析文件类型映射到http media type的逻辑会比较复杂，直接使用bfs url返回的类型
     */
    @Override
    public InvoiceUploadBo uploadInvoice(MultipartFile file) {
        try {
            byte[] fileBytes = file.getBytes();
            BFSUploadResult bfsUploadResult = bfsService.upload(BFSKey.CATEGORY_MINIAPP_OPEN, file.getOriginalFilename(), fileBytes);
            log.info("BFS上传成功，url: {}", bfsUploadResult.getUrl());

            AssertUtil.isTrue(StringUtils.isNotBlank(bfsUploadResult.getUrl()), ErrorCodeType.NO_DATA.getCode(), "上传后未返回图片url");

            String partName = getBfsFileNameFromUrl(bfsUploadResult.getUrl());

            HuilianyiUploadResult huilianyiResult = huilianyiPaymentService.uploadAttachments(bfsUploadResult.getUrl(), partName);
            log.info("汇联易上传成功，oid: {}", huilianyiResult.getOid());

            return InvoiceUploadBo.builder()
                    .oid(huilianyiResult.getOid())
                    .url(bfsUploadResult.getUrl())
                    .build();

        } catch (Exception e) {
            log.error("发票上传失败", e);
            throw new ServiceException("发票上传失败: " + e.getMessage());
        }
    }

    @Override
    public PageResult<SettlementItemBo> getSettlementList(Long mid,
                                                          String appId,
                                                          Integer page,
                                                          Integer size,
                                                          Timestamp beginTime,
                                                          Timestamp endTime,
                                                          Integer settlementStatus) {

        AssertUtil.isTrue(accountService.hasPermission(mid, appId, MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        MiniAppOpenSettlementPoExample.Criteria criteria = example.createCriteria();

        criteria.andAppIdEqualTo(appId);

        if (beginTime != null) {
            criteria.andSettlementBeginTimeGreaterThanOrEqualTo(beginTime);
        }

        if (endTime != null) {
            criteria.andSettlementEndTimeLessThanOrEqualTo(endTime);
        }

        if (settlementStatus != null) {
            criteria.andSettlementStatusEqualTo(settlementStatus);
        }

        criteria.andIsDeletedEqualTo(0);

        long total = settlementDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }

        Page pageInfo = Page.valueOf(page, size);
        example.setLimit(pageInfo.getLimit());
        example.setOffset(pageInfo.getOffset());
        example.setOrderByClause("id desc");

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);


        List<SettlementItemBo> settlementItemList = settlementList.stream()
                .map(this::convertToSettlementItemBo)
                .collect(Collectors.toList());

        return new PageResult<>((int) total, settlementItemList);
    }

    @Override
    public SettlementDetailBo getSettlementDetail(Long mid, String settlementId) {
        MiniAppOpenSettlementPoExample example = new MiniAppOpenSettlementPoExample();
        example.createCriteria()
                .andSettlementIdEqualTo(settlementId)
                .andIsDeletedEqualTo(0);

        List<MiniAppOpenSettlementPo> settlementList = settlementDao.selectByExample(example);
        AssertUtil.isTrue(!CollectionUtils.isEmpty(settlementList), ErrorCodeType.NO_DATA.getCode(), "结算单不存在");

        MiniAppOpenSettlementPo settlement = settlementList.get(0);

        AssertUtil.isTrue(accountService.hasPermission(mid, settlement.getAppId(), MiniAppPermission.SUPER_ADMIN), ErrorCodeType.NO_PERMISSION);

        CompanyDetailBo detail = companyService.getDetail(mid);

        return SettlementDetailBo.builder()
                .settlementId(settlement.getSettlementId())
                .beginTime(settlement.getSettlementBeginTime())
                .endTime(settlement.getSettlementEndTime())
                .companyName(detail.getCompanyInfo().getCompanyName())
                .actualWithdrawAmount(settlement.getActualWithdrawAmount())
                .settlementStatus(settlement.getSettlementStatus())
                .paymentOrderId(settlement.getPaymentOrderId())
                .build();
    }

    private SettlementItemBo convertToSettlementItemBo(MiniAppOpenSettlementPo po) {
        return SettlementItemBo.builder()
                .settlementId(po.getSettlementId())
                .appId(po.getAppId())
                .settlementBeginTime(po.getSettlementBeginTime())
                .settlementEndTime(po.getSettlementEndTime())
                .settlementStatus(po.getSettlementStatus())
                .withdrawApplyAmount(po.getWithdrawApplyAmount())
                .actualWithdrawAmount(po.getActualWithdrawAmount())
                .taxFee(po.getTaxFee())
                .ctime(po.getCtime())
                .mtime(po.getMtime())
                .build();
    }

    private String getBfsFileNameFromUrl(String url) {
        UriComponents uriComponents = UriComponentsBuilder.fromHttpUrl(url).build();
        List<String> pathSegments = uriComponents.getPathSegments();
        return pathSegments.get(pathSegments.size() - 1);
    }

    /**
     * 计算税费：withdraw_apply_amount/(1+6%)*税率，进行四舍五入
     */
    private BigDecimal calculateTaxFee(BigDecimal withdrawApplyAmount, String taxRatio) {
        return withdrawApplyAmount
                .divide(fixedProportionRatio, 2, RoundingMode.HALF_UP)
                .multiply(new BigDecimal(taxRatio));
    }

    /**
     * 计算实际提现金额：withdraw_apply_amount/(1+6%)*(1+税率)
     */
    private BigDecimal calculateActualWithdrawAmount(BigDecimal withdrawApplyAmount, String taxRatio) {
        return withdrawApplyAmount
                .divide(fixedProportionRatio, 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.ONE.add(new BigDecimal(taxRatio)));
    }
}
