package com.bilibili.miniapp.open.service.bo.settlement;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 结算项BO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SettlementItemBo {

    /**
     * 结算单ID
     */
    private String settlementId;

    /**
     * 小程序ID
     */
    private String appId;

    /**
     * 结算开始时间
     */
    private Timestamp settlementBeginTime;

    /**
     * 结算结束时间
     */
    private Timestamp settlementEndTime;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 申请提现金额（分）
     */
    private BigDecimal withdrawApplyAmount;

    /**
     * 实际提现金额（分）
     */
    private BigDecimal actualWithdrawAmount;

    /**
     * 税费（分）
     */
    private BigDecimal taxFee;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;
}
