package com.bilibili.miniapp.open.service.biz.settlement.impl;

import com.bilibili.miniapp.open.common.entity.Response;
import com.bilibili.miniapp.open.service.biz.AbstractOpenService;
import com.bilibili.miniapp.open.service.biz.settlement.HuilianyiPaymentService;
import com.bilibili.miniapp.open.service.biz.settlement.vo.AccrualCreateMandatoryParams;
import com.bilibili.miniapp.open.service.biz.settlement.vo.ExpenseCreateMandatoryParams;
import com.bilibili.miniapp.open.service.bo.FileDownloadInfo;
import com.bilibili.miniapp.open.service.config.IaaSettlementConfiguration.IaaSettlementConfig;
import com.bilibili.miniapp.open.service.rpc.http.HuilianyiPaymentApi;
import com.bilibili.miniapp.open.service.rpc.http.model.*;
import com.bilibili.miniapp.open.service.rpc.http.model.HuilianyiOcrRequest.ReceiptOcrInput;
import com.bilibili.miniapp.open.service.util.DownloadUtil;
import com.bilibili.miniapp.open.service.util.ProbeUtil;
import com.google.common.base.Joiner;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import io.vavr.Tuple;
import io.vavr.Tuple2;
import io.vavr.control.Try;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.component.http.client.BiliCall;
import pleiades.venus.breaker.exception.BiliBreakerRejectedException;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @desc
 * @date 2025/3/19
 */
@Slf4j
@Service
public class HuilianyiPaymentServiceImpl extends AbstractOpenService implements HuilianyiPaymentService {


    @Resource
    private IaaSettlementConfig iaaSettlementConfig;

    @Resource
    private HuilianyiPaymentApi huilianyiPaymentApi;

    @Resource
    private ThreadPoolExecutor iaaSettlementThreadPoolExecutor;

    private LoadingCache<String, String> tokenCache;


    @PostConstruct
    public void init() {

        this.tokenCache = CacheBuilder.newBuilder()
                .expireAfterWrite(iaaSettlementConfig.getHuilianyiTokenCacheSeconds(), TimeUnit.SECONDS)
                .build(new CacheLoader<String, String>() {
                    @Override
                    public String load(String key) throws Exception {
                        return HuilianyiPaymentServiceImpl.this.acquireToken();
                    }
                });


    }


    @Override
    public HuilianyiAccrualCreateResult createAccrual(AccrualCreateMandatoryParams params) {

        HuilianyiAccrualCreateRequest request = params.toHuilianyiAccrualCreateRequest(
                iaaSettlementConfig.getAccrualParams());

        HuilianyiAsyncTaskResult result = Try.of(() -> ProbeUtil.submitAndProbeSync(
                () -> {
                    Response<HuilianyiAsyncTask> r = this.call(
                            "createAccrualAsync", authorization -> huilianyiPaymentApi.createAccrualAsync(
                                    authorization,
                                    toRequestBody(request)
                            ));

                    if (StringUtils.isEmpty(r.getData().getTaskId())) {
                        throw new RuntimeException("提交预提单异步任务失败: " +
                                Optional.ofNullable(r).map(Response::getMessage).orElse(""));
                    }
                    return r.getData().getTaskId();
                },
                taskId -> {

                    Response<HuilianyiAsyncTaskResult> r = this.call(
                            "probeTask", authorization -> huilianyiPaymentApi.probeTask(
                                    taskId));

                    if (r == null || r.getData() == null) {

                        throw new RuntimeException("查询任务失败: " + taskId + " :" +
                                Optional.ofNullable(r).map(Response::getMessage).orElse(""));
                    }
                    return r.getData();

                },

                r -> r.isSuccess() || r.isFailed(),
                iaaSettlementConfig.getHuilianyiAsyncApiProbeMaxTimes(),
                iaaSettlementConfig.getHuilianyiAsyncApiProbeIntervalSeconds()
        )).getOrElseThrow(t -> {
            return new RuntimeException("创建预提单失败: " + t.getMessage(), t);
        });


        if (result.isFailed()) {
            throw new RuntimeException("创建预提单失败: " + result.getMessage());
        }

        return new HuilianyiAccrualCreateResult()
                .setErrorCode(result.getErrorCode())
                .setMessage(result.getMessage())
                .setKey(result.getKey());

    }

    private HuilianyiInvoiceWrapper processInvoice(String invoiceImgUrl) {
        String oid = uploadInvoice(invoiceImgUrl);

        HuilianyiOcrResult huilianyiOcrResult = receiptOcr(invoiceImgUrl);

        return HuilianyiInvoiceWrapper.builder()
                .ocrResult(huilianyiOcrResult)
                .uploadOid(oid)
                .build();
    }

    private String uploadInvoice(String invoiceImgUrl) {
        HuilianyiUploadResult huilianyiUploadResult = uploadAttachments(invoiceImgUrl, generatePartName());
        if (huilianyiUploadResult == null || StringUtils.isEmpty(huilianyiUploadResult.getOid())) {
            throw new IllegalArgumentException("上传发票失败: " + invoiceImgUrl);
        }
        return huilianyiUploadResult.getOid();
    }

    private String generatePartName() {
        return UUID.randomUUID().toString();
    }

    @Override
    public HuilianyiOcrResult receiptOcr(String invoiceImgUrl) {

        HuilianyiOcrRequest request = new HuilianyiOcrRequest()
                .setReceiptOcrInput(
                        new ReceiptOcrInput().setImageURL(invoiceImgUrl)
                                .setNeedSlicingAttachment(true)
                );

        Response<CallWrapper<DataWrapper<HuilianyiOcrResult>>> r = this.call(
                "receiptOcr", authorization -> huilianyiPaymentApi.receiptOcr(
                        authorization,
                        toRequestBody(request)
                ));

        if (r == null || r.getData() == null || r.getData().getRelayJsonStringOutput() == null ||
                r.getData().getRelayJsonStringOutput().getData() == null) {
            throw new IllegalArgumentException(
                    "识别发票失败: " + Optional.ofNullable(r).map(Response::getData)
                            .map(CallWrapper::getRelayJsonStringOutput)
                            .map(DataWrapper::getMessage).orElse(""));
        }

        return r.getData().getRelayJsonStringOutput().getData();
    }


    @Override
    public Map<String, HuilianyiInvoiceWrapper> batchProcessInvoice(List<String> invoiceImgUrls) {

        if (CollectionUtils.isEmpty(invoiceImgUrls)) {
            return Map.of();
        }

        if (invoiceImgUrls.size() == 1) {
            return Map.of(
                    invoiceImgUrls.get(0),
                    processInvoice(invoiceImgUrls.get(0))
            );
        }

        List<Tuple2<String, CompletableFuture<HuilianyiInvoiceWrapper>>> futures = invoiceImgUrls.stream()
                .map(url -> Tuple.of(url, CompletableFuture.supplyAsync(() -> processInvoice(url), iaaSettlementThreadPoolExecutor)))
                .collect(Collectors.toList());

        return CompletableFuture.allOf(futures.stream().map(f -> f._2).toArray(CompletableFuture[]::new))
                .thenApplyAsync(v -> {

                    Map<String, HuilianyiInvoiceWrapper> r = futures.stream()
                            .collect(Collectors.toMap(
                                    tuple -> tuple._1,
                                    tuple -> tuple._2.join(),
                                    (a, b) -> b
                            ));

                    return r;
                }, iaaSettlementThreadPoolExecutor)
                .exceptionally(ex -> {
                    log.error("Fail to get ocr result for urls={}", invoiceImgUrls, ex);
                    return Collections.emptyMap();
                }).join();
    }


    @Override
    public HuilianyiExpenseCreateResult createExpense(ExpenseCreateMandatoryParams expenseCreateMandatoryParams) {

        HuilianyiExpenseCreateRequest request = expenseCreateMandatoryParams
                .toHuilianyiExpenseCreateRequest(iaaSettlementConfig.getExpenseParams());

        HuilianyiAsyncTaskResult result = Try.of(() -> ProbeUtil.submitAndProbeSync(
                () -> {
                    Response<HuilianyiAsyncTask> r = this.call(
                            "createExpenseAsync", authorization -> huilianyiPaymentApi.createExpenseAsync(
                                    authorization,
                                    toRequestBody(request)
                            ));

                    if (StringUtils.isEmpty(r.getData().getTaskId())) {
                        throw new RuntimeException("提交付款单异步任务失败: " +
                                Optional.ofNullable(r).map(Response::getMessage).orElse(""));
                    }
                    return r.getData().getTaskId();
                },
                taskId -> {

                    Response<HuilianyiAsyncTaskResult> r = this.call(
                            "probeTask", authorization -> huilianyiPaymentApi.probeTask(
                                    taskId));

                    if (r == null || r.getData() == null) {

                        throw new RuntimeException("查询任务失败: " + taskId + " :" +
                                Optional.ofNullable(r).map(Response::getMessage).orElse(""));
                    }
                    return r.getData();

                },
                r -> r.isSuccess() || r.isFailed(),

                iaaSettlementConfig.getHuilianyiAsyncApiProbeMaxTimes(),
                iaaSettlementConfig.getHuilianyiAsyncApiProbeIntervalSeconds()
        )).getOrElseThrow(t -> {
            return new RuntimeException("创建付款单失败: " + t.getMessage(), t);
        });


        if (result.isFailed()) {
            throw new RuntimeException("创建付款单失败: " + result.getMessage());
        }

        return new HuilianyiExpenseCreateResult()
                .setErrorCode(result.getErrorCode())
                .setMessage(result.getMessage())
                .setKey(result.getKey());


    }

    public HuilianyiUploadResult uploadAttachments(String url, String partName) {

        Response<CallWrapper<HuilianyiUploadResult>> r = this.call(
                "uploadAttachments", authorization -> huilianyiPaymentApi.uploadAttachments(
                        authorization,
                        urlToMultipartBodyPart(url, partName)
                ));
        if (r == null || r.getData() == null || r.getData().getRelayJsonStringOutput() == null ||
                r.getData().getRelayJsonStringOutput().getOid() == null) {
            throw new IllegalArgumentException(
                    "上传文件失败: " + Optional.ofNullable(r).map(Response::getData)
                            .map(CallWrapper::getRelayJsonStringOutput)
                            .map(HuilianyiUploadResult::getMessage)
                            .orElse(""));
        }

        return r.getData().getRelayJsonStringOutput();
    }



    private MultipartBody.Part urlToMultipartBodyPart(String fileUrl, String partName) {
        // 下载文件
        FileDownloadInfo fileDownloadInfo = DownloadUtil.downloadFile(fileUrl);

        // 创建RequestBody
        assert fileDownloadInfo != null;

        MediaType mediaType = MediaType.parse(fileDownloadInfo.getMedia());
        RequestBody requestBody = RequestBody.create(fileDownloadInfo.getBytes(), mediaType);


        // 创建 MultipartBody.Part 使用文件名，需要提供 part name 和 RequestBody
        return MultipartBody.Part.createFormData("file", URLEncoder.encode(partName + "." + mediaType.subtype(), StandardCharsets.UTF_8), requestBody);
    }


    private <T> Response<T> call(String method, Function<String, BiliCall<Response<T>>> function) {

        String token = getTokenByCache();

        Response<T> r = super.callWithoutWrap(
                method,
                () -> function.apply("Bearer " + token)
        );

        if (r != null && r.getCode() == 10009) {

            String tokenRefreshed = kickAndRefreshToken();
            r = super.callWithoutWrap(
                    method,
                    () -> function.apply("Bearer " + tokenRefreshed)
            );
        }

        return r;
    }


    private String getTokenByCache() {

        try {
            return tokenCache.get("_dummy");
        } catch (ExecutionException e) {
            throw new RuntimeException("获取汇联易中间层token失败", e);
        }
    }


    private String kickAndRefreshToken() {
        tokenCache.invalidateAll();
        return getTokenByCache();
    }


    public String acquireToken() throws BiliBreakerRejectedException {

        long time = System.currentTimeMillis();

        String string = Joiner.on(":").join(
                iaaSettlementConfig.getHuilianyiServiceName(),
                iaaSettlementConfig.getHuilianyiSecret(),
                time
        );


        Response<HuilianyiTokenResult> r = super.callWithoutWrap(
                "huilianyiPaymentApi.acquireToken",
                () -> huilianyiPaymentApi.acquireToken(
                        toRequestBody(new HuilianyiTokenAcquireRequest()
                                .setAskString(
                                        Base64.getEncoder().encodeToString(string.getBytes(StandardCharsets.UTF_8)))
                        )
                )
        );

        if (r == null || r.getData() == null || StringUtils.isEmpty(r.getData().getToken())) {
            throw new RuntimeException(
                    "获取汇联易中间层token失败: " + Optional.ofNullable(r).map(Response::getMessage).orElse(""));
        }

        log.info("Success to acquire token from huilianyiPaymentApi.acquireToken, token={}", r.getData().getToken());
        return r.getData().getToken();

    }


}
