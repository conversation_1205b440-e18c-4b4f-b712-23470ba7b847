package com.bilibili.miniapp.open.service.biz.income;

import com.bilibili.miniapp.open.service.bo.income.IncomeDetailQueryBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeDetailBo;
import com.bilibili.miniapp.open.service.bo.income.IncomeSummaryBo;

/**
 * 收入服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface IIncomeService {

    /**
     * 获取收入汇总
     *
     * @param mid 用户ID
     * @return 收入汇总信息
     */
    IncomeSummaryBo getIncomeSummary(Long mid);

    /**
     * 获取收入明细
     *
     * @param mid 用户ID
     * @param request 查询请求
     * @return 收入明细信息
     */
    IncomeDetailBo getIncomeDetails(Long mid, IncomeDetailQueryBo request);

    void processDailyIncomeDetails(String logDate);

    void doProcessDailyIncomeDetails(String logDate);
}
