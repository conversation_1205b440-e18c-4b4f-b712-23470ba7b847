package com.bilibili.miniapp.open.portal.vo.settlement;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * 结算详情VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SettlementDetailVo {

    /**
     * 结算单ID
     */
    private String settlementId;

    /**
     * 结算开始时间
     */
    private String beginTime;

    /**
     * 结算结束时间
     */
    private String endTime;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 实际提现金额（分）
     */
    private String actualWithdrawAmount;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 汇联易付款单id
     */
    private String paymentOrderId;
}
