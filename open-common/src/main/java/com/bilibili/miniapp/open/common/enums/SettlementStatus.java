package com.bilibili.miniapp.open.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/6/6
 */
@AllArgsConstructor
@Getter
public enum SettlementStatus {
    CANCELED(-1, "已作废"),
    PAYMENT_FAILED(-3, "付款失败"),
    AUDIT_REJECTED(-2, "审核驳回"),
    PENDING_CONFIRMATION(0, "待确认"),
    PENDING_UPLOAD_INVOICE(1, "待上传发票"),
    PENDING_AUDIT(2, "审核中"),
    PENDING_PAYMENT(3, "代付款"),
    PAYMENT_SUCCESS(4, "付款成功"),
    ;
    private final int code;
    private final String desc;

}
