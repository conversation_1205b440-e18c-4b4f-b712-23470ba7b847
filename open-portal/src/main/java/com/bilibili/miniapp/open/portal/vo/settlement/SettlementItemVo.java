package com.bilibili.miniapp.open.portal.vo.settlement;

import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 结算项VO
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonNaming(PropertyNamingStrategies.SnakeCaseStrategy.class)
public class SettlementItemVo {

    /**
     * 结算单ID
     */
    private String settlementId;

    /**
     * 小程序ID
     */
    private String appId;

    /**
     * 结算开始时间
     */
    private Timestamp settlementBeginTime;

    /**
     * 结算结束时间
     */
    private Timestamp settlementEndTime;

    /**
     * 结算状态
     */
    private Integer settlementStatus;

    /**
     * 申请提现金额（元）
     */
    private String withdrawApplyAmount;

    /**
     * 实际提现金额（元）
     */
    private String actualWithdrawAmount;

    /**
     * 税费（元）
     */
    private String taxFee;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;
}
