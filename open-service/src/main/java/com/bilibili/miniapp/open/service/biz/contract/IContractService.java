package com.bilibili.miniapp.open.service.biz.contract;

import com.bilibili.miniapp.open.common.enums.ContractStatusEnum;
import com.bilibili.miniapp.open.service.bo.contract.ContractSettlementDetailRespBo;
import com.bilibili.miniapp.open.service.bo.contract.ContractSignUrlBo;
import com.bilibili.miniapp.open.service.bo.contract.SettlementContractBo;

/**
 * 合同结算服务接口
 *
 * <AUTHOR>
 * @date 2025/1/15
 */
public interface IContractService {

    void saveContractSettlement(Long mid, SettlementContractBo request);

    ContractSettlementDetailRespBo getContractDetail(Long mid, String appId);

    ContractSignUrlBo getContractSignUrl(Long mid, String appId);

    void updateContractStatus(String contractId, ContractStatusEnum status, long sendMsgTimeForSecond);
}
